// Professional animation utilities for military-grade UI/UX

export const animations = {
  // Fade animations
  fadeIn: 'animate-in fade-in duration-300 ease-out',
  fadeOut: 'animate-out fade-out duration-200 ease-in',
  fadeInUp: 'animate-in fade-in slide-in-from-bottom-4 duration-300 ease-out',
  fadeInDown: 'animate-in fade-in slide-in-from-top-4 duration-300 ease-out',
  fadeInLeft: 'animate-in fade-in slide-in-from-left-4 duration-300 ease-out',
  fadeInRight: 'animate-in fade-in slide-in-from-right-4 duration-300 ease-out',

  // Scale animations
  scaleIn: 'animate-in zoom-in-95 duration-200 ease-out',
  scaleOut: 'animate-out zoom-out-95 duration-150 ease-in',

  // Slide animations
  slideInFromLeft: 'animate-in slide-in-from-left-full duration-300 ease-out',
  slideInFromRight: 'animate-in slide-in-from-right-full duration-300 ease-out',
  slideInFromTop: 'animate-in slide-in-from-top-full duration-300 ease-out',
  slideInFromBottom: 'animate-in slide-in-from-bottom-full duration-300 ease-out',

  // Hover effects
  hoverScale: 'transition-transform duration-200 ease-out hover:scale-105',
  hoverLift: 'transition-all duration-200 ease-out hover:shadow-lg hover:-translate-y-1',
  hoverGlow: 'transition-all duration-200 ease-out hover:shadow-primary-500/25 hover:shadow-lg',

  // Button animations
  buttonPress: 'transition-all duration-150 ease-out active:scale-95',
  buttonHover: 'transition-all duration-200 ease-out hover:shadow-md hover:brightness-110',

  // Card animations
  cardHover: 'transition-all duration-300 ease-out hover:shadow-xl hover:shadow-primary-500/10 hover:-translate-y-2',
  cardPress: 'transition-all duration-150 ease-out active:scale-98',

  // Loading animations
  pulse: 'animate-pulse',
  spin: 'animate-spin',
  bounce: 'animate-bounce',
  ping: 'animate-ping',

  // Chart animations
  chartFadeIn: 'animate-in fade-in duration-500 ease-out',
  chartSlideUp: 'animate-in slide-in-from-bottom-8 fade-in duration-600 ease-out',

  // Stagger animations (for lists)
  staggerChild: (_index: number) => `animate-in fade-in slide-in-from-bottom-4 duration-300 ease-out`,
  staggerDelay: (index: number) => `delay-${Math.min(index * 100, 500)}`,

  // Professional transitions
  smooth: 'transition-all duration-300 ease-out',
  fast: 'transition-all duration-150 ease-out',
  slow: 'transition-all duration-500 ease-out',
};

// Animation presets for common components
export const componentAnimations = {
  modal: {
    overlay: 'animate-in fade-in duration-200',
    content: 'animate-in fade-in zoom-in-95 duration-200 ease-out',
  },
  dropdown: {
    content: 'animate-in fade-in slide-in-from-top-2 duration-150 ease-out',
  },
  tooltip: {
    content: 'animate-in fade-in zoom-in-95 duration-150 ease-out',
  },
  notification: {
    enter: 'animate-in slide-in-from-right-full fade-in duration-300 ease-out',
    exit: 'animate-out slide-out-to-right-full fade-out duration-200 ease-in',
  },
  sidebar: {
    enter: 'animate-in slide-in-from-left-full duration-300 ease-out',
    exit: 'animate-out slide-out-to-left-full duration-200 ease-in',
  },
  tab: {
    content: 'animate-in fade-in slide-in-from-bottom-2 duration-200 ease-out',
  },
  chart: {
    container: 'animate-in fade-in duration-500 ease-out',
    legend: 'animate-in fade-in slide-in-from-top-2 duration-300 ease-out delay-200',
    indicators: 'animate-in fade-in slide-in-from-bottom-2 duration-400 ease-out delay-300',
  },
};

// Utility functions for dynamic animations
export const createStaggerAnimation = (totalItems: number, baseDelay = 50) => {
  return Array.from({ length: totalItems }, (_, index) => ({
    animationDelay: `${index * baseDelay}ms`,
    className: animations.fadeInUp,
  }));
};

export const createSequentialAnimation = (items: string[], delay = 100) => {
  return items.map((item, index) => ({
    item,
    delay: index * delay,
    className: animations.fadeInUp,
  }));
};

// Professional easing curves
export const easingCurves = {
  easeOutCubic: 'cubic-bezier(0.33, 1, 0.68, 1)',
  easeInOutCubic: 'cubic-bezier(0.65, 0, 0.35, 1)',
  easeOutQuart: 'cubic-bezier(0.25, 1, 0.5, 1)',
  easeInOutQuart: 'cubic-bezier(0.76, 0, 0.24, 1)',
  easeOutExpo: 'cubic-bezier(0.19, 1, 0.22, 1)',
  easeInOutExpo: 'cubic-bezier(0.87, 0, 0.13, 1)',
};

// Animation configuration for different contexts
export const animationConfig = {
  development: {
    duration: 300,
    easing: easingCurves.easeOutCubic,
    reducedMotion: false,
  },
  production: {
    duration: 200,
    easing: easingCurves.easeOutQuart,
    reducedMotion: true, // Respect user preferences
  },
};

// Utility to check for reduced motion preference
export const prefersReducedMotion = () => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Apply animations conditionally based on user preferences
export const conditionalAnimation = (animationClass: string) => {
  return prefersReducedMotion() ? '' : animationClass;
};
