import { OHLCV, AdvancedTechnicalIndicators } from '../types';
import { SMA, EMA, ATR } from 'technicalindicators';
import { logger } from '../utils/logger';

export interface MarketCondition {
  regime: 'trending' | 'ranging' | 'volatile' | 'breakout';
  direction: 'bullish' | 'bearish' | 'neutral';
  strength: number; // 0-100
  volatility: 'low' | 'medium' | 'high';
  confidence: number; // 0-100
  characteristics: string[];
  recommendations: string[];
}

export interface MarketEnvironment {
  overall: MarketCondition;
  shortTerm: MarketCondition; // 1-5 days
  mediumTerm: MarketCondition; // 1-4 weeks
  longTerm: MarketCondition; // 1-3 months
  riskLevel: 'low' | 'medium' | 'high' | 'extreme';
  tradingStrategy: 'trend_following' | 'mean_reversion' | 'breakout' | 'defensive';
}

export class MarketConditionService {
  /**
   * Analyze current market conditions
   */
  public analyzeMarketConditions(
    ohlcvData: OHLCV[],
    indicators: AdvancedTechnicalIndicators
  ): MarketEnvironment {
    try {
      if (ohlcvData.length < 100) {
        throw new Error('Insufficient data for market condition analysis');
      }

      const overall = this.analyzeOverallCondition(ohlcvData, indicators);
      const shortTerm = this.analyzeShortTermCondition(ohlcvData.slice(-20), indicators);
      const mediumTerm = this.analyzeMediumTermCondition(ohlcvData.slice(-60), indicators);
      const longTerm = this.analyzeLongTermCondition(ohlcvData.slice(-120), indicators);

      const riskLevel = this.assessRiskLevel(overall, indicators);
      const tradingStrategy = this.recommendTradingStrategy(overall, shortTerm, mediumTerm);

      logger.info('Market condition analysis completed', {
        regime: overall.regime,
        direction: overall.direction,
        riskLevel,
        strategy: tradingStrategy
      });

      return {
        overall,
        shortTerm,
        mediumTerm,
        longTerm,
        riskLevel,
        tradingStrategy
      };
    } catch (error) {
      logger.error('Error analyzing market conditions:', error);
      throw error;
    }
  }

  /**
   * Analyze overall market condition
   */
  private analyzeOverallCondition(
    ohlcvData: OHLCV[],
    indicators: AdvancedTechnicalIndicators
  ): MarketCondition {
    const prices = ohlcvData.map(candle => candle.close);
    const highs = ohlcvData.map(candle => candle.high);
    const lows = ohlcvData.map(candle => candle.low);

    // Calculate trend strength
    const trendStrength = this.calculateTrendStrength(prices);
    
    // Calculate volatility
    const volatility = this.calculateVolatility(ohlcvData);
    
    // Determine regime
    const regime = this.determineRegime(trendStrength, volatility, indicators);
    
    // Determine direction
    const direction = this.determineDirection(indicators);
    
    // Calculate confidence
    const confidence = this.calculateConfidence(indicators, trendStrength);
    
    // Generate characteristics and recommendations
    const volatilityLevel = volatility > 0.03 ? 'high' : volatility > 0.015 ? 'medium' : 'low';
    const characteristics = this.generateCharacteristics(regime, direction, volatility, indicators);
    const recommendations = this.generateRecommendations(regime, direction, volatilityLevel);

    return {
      regime,
      direction,
      strength: trendStrength,
      volatility: volatilityLevel,
      confidence,
      characteristics,
      recommendations
    };
  }

  /**
   * Analyze short-term market condition (1-5 days)
   */
  private analyzeShortTermCondition(
    recentData: OHLCV[],
    indicators: AdvancedTechnicalIndicators
  ): MarketCondition {
    const prices = recentData.map(candle => candle.close);
    const shortTermTrend = this.calculateShortTermTrend(prices);
    const momentum = this.calculateMomentum(recentData);
    
    let regime: MarketCondition['regime'] = 'ranging';
    let direction: MarketCondition['direction'] = 'neutral';
    
    if (Math.abs(momentum) > 0.02) {
      regime = 'trending';
      direction = momentum > 0 ? 'bullish' : 'bearish';
    } else if (indicators.atr > prices[prices.length - 1] * 0.03) {
      regime = 'volatile';
    }

    return {
      regime,
      direction,
      strength: Math.abs(momentum) * 100,
      volatility: indicators.atr > prices[prices.length - 1] * 0.03 ? 'high' : 'medium',
      confidence: 70,
      characteristics: [`Short-term ${regime} market`, `${direction} bias`],
      recommendations: this.generateShortTermRecommendations(regime, direction)
    };
  }

  /**
   * Analyze medium-term market condition (1-4 weeks)
   */
  private analyzeMediumTermCondition(
    mediumData: OHLCV[],
    indicators: AdvancedTechnicalIndicators
  ): MarketCondition {
    const prices = mediumData.map(candle => candle.close);
    const mediumTrend = this.calculateMediumTermTrend(prices);
    
    let regime: MarketCondition['regime'] = 'ranging';
    let direction: MarketCondition['direction'] = 'neutral';
    
    if (indicators.sma.crossover === 'bullish') {
      regime = 'trending';
      direction = 'bullish';
    } else if (indicators.sma.crossover === 'bearish') {
      regime = 'trending';
      direction = 'bearish';
    }

    return {
      regime,
      direction,
      strength: indicators.patternRecognition.trendStrength,
      volatility: 'medium',
      confidence: 75,
      characteristics: [`Medium-term ${regime} market`, `SMA crossover: ${indicators.sma.crossover}`],
      recommendations: this.generateMediumTermRecommendations(regime, direction)
    };
  }

  /**
   * Analyze long-term market condition (1-3 months)
   */
  private analyzeLongTermCondition(
    longData: OHLCV[],
    indicators: AdvancedTechnicalIndicators
  ): MarketCondition {
    const direction = indicators.patternRecognition.trendDirection === 'uptrend' ? 'bullish' :
                     indicators.patternRecognition.trendDirection === 'downtrend' ? 'bearish' : 'neutral';
    
    const regime = indicators.patternRecognition.trendStrength > 50 ? 'trending' : 'ranging';

    return {
      regime,
      direction,
      strength: indicators.patternRecognition.trendStrength,
      volatility: 'medium',
      confidence: 80,
      characteristics: [`Long-term ${direction} trend`, `Trend strength: ${indicators.patternRecognition.trendStrength.toFixed(0)}%`],
      recommendations: this.generateLongTermRecommendations(regime, direction)
    };
  }

  /**
   * Calculate trend strength
   */
  private calculateTrendStrength(prices: number[]): number {
    if (prices.length < 20) return 0;
    
    const sma20 = SMA.calculate({ values: prices, period: 20 });
    const sma50 = SMA.calculate({ values: prices, period: 50 });
    
    if (sma20.length === 0 || sma50.length === 0) return 0;
    
    const currentPrice = prices[prices.length - 1];
    const currentSMA20 = sma20[sma20.length - 1];
    const currentSMA50 = sma50[sma50.length - 1];
    
    // Calculate alignment score
    let alignmentScore = 0;
    if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {
      alignmentScore = 100; // Perfect bullish alignment
    } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {
      alignmentScore = 100; // Perfect bearish alignment
    } else {
      alignmentScore = 50; // Mixed signals
    }
    
    return alignmentScore;
  }

  /**
   * Calculate volatility
   */
  private calculateVolatility(ohlcvData: OHLCV[]): number {
    const returns = [];
    for (let i = 1; i < ohlcvData.length; i++) {
      const dailyReturn = (ohlcvData[i].close - ohlcvData[i - 1].close) / ohlcvData[i - 1].close;
      returns.push(dailyReturn);
    }
    
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
    
    return Math.sqrt(variance);
  }

  /**
   * Determine market regime
   */
  private determineRegime(
    trendStrength: number,
    volatility: number,
    indicators: AdvancedTechnicalIndicators
  ): MarketCondition['regime'] {
    if (volatility > 0.04) {
      return 'volatile';
    }
    
    if (trendStrength > 70) {
      return 'trending';
    }

    if (indicators.adx < 20) {
      return 'ranging';
    }

    return 'ranging';
  }

  /**
   * Determine market direction
   */
  private determineDirection(indicators: AdvancedTechnicalIndicators): MarketCondition['direction'] {
    let bullishSignals = 0;
    let bearishSignals = 0;
    
    // RSI
    if (indicators.rsi < 30) bullishSignals++;
    else if (indicators.rsi > 70) bearishSignals++;
    
    // MACD
    if (indicators.macd.histogram > 0) bullishSignals++;
    else if (indicators.macd.histogram < 0) bearishSignals++;
    
    // SMA
    if (indicators.sma.crossover === 'bullish') bullishSignals++;
    else if (indicators.sma.crossover === 'bearish') bearishSignals++;
    
    // Volume
    if (indicators.volumeAnalysis.volumeTrend === 'increasing') bullishSignals++;
    
    // Pattern recognition
    if (indicators.patternRecognition.trendDirection === 'uptrend') bullishSignals++;
    else if (indicators.patternRecognition.trendDirection === 'downtrend') bearishSignals++;
    
    if (bullishSignals > bearishSignals) return 'bullish';
    if (bearishSignals > bullishSignals) return 'bearish';
    return 'neutral';
  }

  /**
   * Calculate confidence level
   */
  private calculateConfidence(
    indicators: AdvancedTechnicalIndicators,
    trendStrength: number
  ): number {
    let confidence = 50; // Base confidence
    
    // Add confidence based on indicator alignment
    if (indicators.sma.crossover !== 'neutral') confidence += 15;
    if (indicators.macd.histogram !== 0) confidence += 10;
    if (indicators.volumeAnalysis.volumeTrend !== 'neutral') confidence += 10;
    if (trendStrength > 70) confidence += 15;
    
    return Math.min(100, confidence);
  }

  /**
   * Generate market characteristics
   */
  private generateCharacteristics(
    regime: MarketCondition['regime'],
    direction: MarketCondition['direction'],
    volatility: number,
    indicators: AdvancedTechnicalIndicators
  ): string[] {
    const characteristics = [];
    
    characteristics.push(`${regime.charAt(0).toUpperCase() + regime.slice(1)} market regime`);
    characteristics.push(`${direction.charAt(0).toUpperCase() + direction.slice(1)} directional bias`);
    
    if (volatility > 0.03) {
      characteristics.push('High volatility environment');
    } else if (volatility < 0.01) {
      characteristics.push('Low volatility environment');
    }
    
    if (indicators.volumeAnalysis.volumeTrend === 'increasing') {
      characteristics.push('Increasing volume participation');
    }
    
    if (indicators.patternRecognition.patterns.length > 0) {
      characteristics.push(`Patterns detected: ${indicators.patternRecognition.patterns.join(', ')}`);
    }
    
    return characteristics;
  }

  /**
   * Generate trading recommendations
   */
  private generateRecommendations(
    regime: MarketCondition['regime'],
    direction: MarketCondition['direction'],
    volatility: string
  ): string[] {
    const recommendations = [];
    
    switch (regime) {
      case 'trending':
        recommendations.push('Consider trend-following strategies');
        recommendations.push('Use momentum indicators for entry/exit');
        break;
      case 'ranging':
        recommendations.push('Consider mean-reversion strategies');
        recommendations.push('Use support/resistance levels for trading');
        break;
      case 'volatile':
        recommendations.push('Reduce position sizes');
        recommendations.push('Use wider stop losses');
        break;
      case 'breakout':
        recommendations.push('Watch for breakout confirmations');
        recommendations.push('Consider breakout trading strategies');
        break;
    }
    
    if (volatility === 'high') {
      recommendations.push('Exercise extra caution with risk management');
    }
    
    return recommendations;
  }

  /**
   * Assess overall risk level
   */
  private assessRiskLevel(
    condition: MarketCondition,
    indicators: AdvancedTechnicalIndicators
  ): MarketEnvironment['riskLevel'] {
    let riskScore = 0;
    
    if (condition.volatility === 'high') riskScore += 3;
    else if (condition.volatility === 'medium') riskScore += 2;
    else riskScore += 1;
    
    if (condition.confidence < 50) riskScore += 2;
    if (indicators.rsi > 80 || indicators.rsi < 20) riskScore += 1;
    if (condition.regime === 'volatile') riskScore += 2;
    
    if (riskScore >= 7) return 'extreme';
    if (riskScore >= 5) return 'high';
    if (riskScore >= 3) return 'medium';
    return 'low';
  }

  /**
   * Recommend trading strategy
   */
  private recommendTradingStrategy(
    overall: MarketCondition,
    shortTerm: MarketCondition,
    mediumTerm: MarketCondition
  ): MarketEnvironment['tradingStrategy'] {
    if (overall.regime === 'trending' && overall.strength > 70) {
      return 'trend_following';
    }
    
    if (overall.regime === 'ranging' && overall.volatility === 'low') {
      return 'mean_reversion';
    }
    
    if (overall.regime === 'breakout' || shortTerm.regime === 'breakout') {
      return 'breakout';
    }
    
    if (overall.volatility === 'high' || overall.confidence < 50) {
      return 'defensive';
    }
    
    return 'trend_following'; // Default
  }

  // Helper methods for timeframe-specific analysis
  private calculateShortTermTrend(prices: number[]): number {
    if (prices.length < 5) return 0;
    const start = prices[0];
    const end = prices[prices.length - 1];
    return (end - start) / start;
  }

  private calculateMomentum(data: OHLCV[]): number {
    if (data.length < 3) return 0;
    const recent = data.slice(-3);
    const avgClose = recent.reduce((sum, candle) => sum + candle.close, 0) / recent.length;
    const firstClose = data[0].close;
    return (avgClose - firstClose) / firstClose;
  }

  private calculateMediumTermTrend(prices: number[]): number {
    if (prices.length < 20) return 0;
    const sma = SMA.calculate({ values: prices, period: 20 });
    if (sma.length < 2) return 0;
    return (sma[sma.length - 1] - sma[0]) / sma[0];
  }

  private generateShortTermRecommendations(regime: string, direction: string): string[] {
    if (regime === 'trending' && direction === 'bullish') return ['Look for intraday buying opportunities'];
    if (regime === 'trending' && direction === 'bearish') return ['Look for intraday selling opportunities'];
    return ['Stay cautious, wait for clearer signals'];
  }

  private generateMediumTermRecommendations(regime: string, direction: string): string[] {
    if (regime === 'trending' && direction === 'bullish') return ['Hold long positions, consider adding on dips'];
    if (regime === 'trending' && direction === 'bearish') return ['Hold short positions, consider adding on rallies'];
    return ['Monitor for a breakout from the current range'];
  }

  private generateLongTermRecommendations(regime: string, direction: string): string[] {
    if (regime === 'trending' && direction === 'bullish') return ['Maintain a long-term bullish outlook'];
    if (regime === 'trending' && direction === 'bearish') return ['Maintain a long-term bearish outlook'];
    return ['Neutral long-term outlook, wait for a trend to develop'];
  }
}
