# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/trading-bot

# API Keys Configuration (Optional - system will use mock data if not provided)

# Google AI Configuration - for advanced AI analysis and natural language processing
# Get your Google AI API key at: https://makersuite.google.com/app/apikey
GOOGLE_AI_API_KEY=AIzaSyA7j27X9SXmvXwW61RwvAyHWJie6YrBDeA

# News API Configuration - for news sentiment analysis
# Get your NewsAPI key at: https://newsapi.org/
NEWS_API_KEY=********************************

# Alpha Vantage - for comprehensive market data
# Get your Alpha Vantage API key at: https://www.alphavantage.co/support/#api-key
ALPHA_VANTAGE_API_KEY=41U5JV4DKYKL8NFS

# Polygon.io - for high-frequency market data and real-time quotes
# Get your Polygon API key at: https://polygon.io/
POLYGON_API_KEY=********************************

# IEX Cloud - alternative market data provider
# Get your IEX Cloud API key at: https://iexcloud.io/
IEX_CLOUD_API_KEY=

# Trading Configuration
MAX_RISK_PER_TRADE=0.02
ATR_MULTIPLIER_SL=2
ATR_MULTIPLIER_TP=4
SENTIMENT_LOOKBACK_HOURS=24

# Logging
LOG_LEVEL=info 