import React, { useEffect, useRef } from 'react';
import { create<PERSON>hart, IChartApi, LineData } from 'lightweight-charts';
import { AdvancedTechnicalIndicators } from '../../types';

interface TechnicalIndicatorChartProps {
  indicators: AdvancedTechnicalIndicators;
  height?: number;
  timeData: Date[];
}

export const TechnicalIndicatorChart: React.FC<TechnicalIndicatorChartProps> = ({
  indicators,
  height = 300,
  timeData
}) => {
  // Early return if indicators are not properly structured
  if (!indicators || !indicators.rsi || !indicators.macd || !indicators.stochastic || !indicators.williamsR) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading technical indicators...</div>
      </div>
    );
  }
  const rsiChartRef = useRef<HTMLDivElement>(null);
  const macdChartRef = useRef<HTMLDivElement>(null);
  const stochasticChartRef = useRef<HTMLDivElement>(null);
  const rsiChartInstanceRef = useRef<IChartApi | null>(null);
  const macdChartInstanceRef = useRef<IChartApi | null>(null);
  const stochasticChartInstanceRef = useRef<IChartApi | null>(null);

  useEffect(() => {
    if (!timeData.length) return;

    // Cleanup existing charts
    if (rsiChartInstanceRef.current) {
      rsiChartInstanceRef.current.remove();
      rsiChartInstanceRef.current = null;
    }
    if (macdChartInstanceRef.current) {
      macdChartInstanceRef.current.remove();
      macdChartInstanceRef.current = null;
    }
    if (stochasticChartInstanceRef.current) {
      stochasticChartInstanceRef.current.remove();
      stochasticChartInstanceRef.current = null;
    }

    // Create RSI Chart
    if (rsiChartRef.current) {
      const rsiChart = createChart(rsiChartRef.current, {
        width: rsiChartRef.current.clientWidth,
        height: height / 3,
        layout: {
          background: { color: '#ffffff' },
          textColor: '#333333',
        },
        grid: {
          vertLines: { color: '#f0f0f0' },
          horzLines: { color: '#f0f0f0' },
        },
        rightPriceScale: {
          borderColor: '#cccccc',
        },
        timeScale: {
          borderColor: '#cccccc',
          visible: false,
        },
      });

      const rsiSeries = rsiChart.addSeries('Line', {
        color: '#3b82f6', // primary-500
        lineWidth: 2,
        title: 'RSI',
      });

      // Add RSI overbought/oversold lines
      const overboughtSeries = rsiChart.addSeries('Line', {
        color: '#ef4444', // danger-500
        lineWidth: 1,
        lineStyle: 2, // dashed
        title: 'Overbought (70)',
      });

      const oversoldSeries = rsiChart.addSeries('Line', {
        color: '#22c55e', // success-500
        lineWidth: 1,
        lineStyle: 2, // dashed
        title: 'Oversold (30)',
      });

      const rsiData: LineData[] = timeData.map(time => ({
        time: Math.floor(time.getTime() / 1000) as any,
        value: indicators.rsi,
      }));

      const overboughtData: LineData[] = timeData.map(time => ({
        time: Math.floor(time.getTime() / 1000) as any,
        value: 70,
      }));

      const oversoldData: LineData[] = timeData.map(time => ({
        time: Math.floor(time.getTime() / 1000) as any,
        value: 30,
      }));

      rsiSeries.setData(rsiData);
      overboughtSeries.setData(overboughtData);
      oversoldSeries.setData(oversoldData);

      // Set RSI scale
      rsiChart.priceScale('right').applyOptions({
        scaleMargins: {
          top: 0.1,
          bottom: 0.1,
        },
      });

      rsiChartInstanceRef.current = rsiChart;
    }

    // Create MACD Chart
    if (macdChartRef.current) {
      const macdChart = createChart(macdChartRef.current, {
        width: macdChartRef.current.clientWidth,
        height: height / 3,
        layout: {
          background: { color: '#ffffff' },
          textColor: '#333333',
        },
        grid: {
          vertLines: { color: '#f0f0f0' },
          horzLines: { color: '#f0f0f0' },
        },
        rightPriceScale: {
          borderColor: '#cccccc',
        },
        timeScale: {
          borderColor: '#cccccc',
          visible: false,
        },
      });

      const macdSeries = macdChart.addSeries('Line', {
        color: '#3b82f6', // primary-500
        lineWidth: 2,
        title: 'MACD',
      });

      const signalSeries = macdChart.addSeries('Line', {
        color: '#f59e0b', // warning-500
        lineWidth: 2,
        title: 'Signal',
      });

      const histogramSeries = macdChart.addSeries('Histogram', {
        color: '#8b5cf6', // purple-500
        title: 'Histogram',
      });

      const macdData: LineData[] = timeData.map(time => ({
        time: Math.floor(time.getTime() / 1000) as any,
        value: indicators.macd.macd,
      }));

      const signalData: LineData[] = timeData.map(time => ({
        time: Math.floor(time.getTime() / 1000) as any,
        value: indicators.macd.signal,
      }));

      const histogramData = timeData.map(time => ({
        time: Math.floor(time.getTime() / 1000) as any,
        value: indicators.macd.histogram,
        color: indicators.macd.histogram >= 0 ? '#22c55e' : '#ef4444',
      }));

      macdSeries.setData(macdData);
      signalSeries.setData(signalData);
      histogramSeries.setData(histogramData);

      macdChartInstanceRef.current = macdChart;
    }

    // Create Stochastic Chart
    if (stochasticChartRef.current) {
      const stochasticChart = createChart(stochasticChartRef.current, {
        width: stochasticChartRef.current.clientWidth,
        height: height / 3,
        layout: {
          background: { color: '#ffffff' },
          textColor: '#333333',
        },
        grid: {
          vertLines: { color: '#f0f0f0' },
          horzLines: { color: '#f0f0f0' },
        },
        rightPriceScale: {
          borderColor: '#cccccc',
        },
        timeScale: {
          borderColor: '#cccccc',
        },
      });

      const kSeries = stochasticChart.addSeries('Line', {
        color: '#3b82f6', // primary-500
        lineWidth: 2,
        title: '%K',
      });

      const dSeries = stochasticChart.addSeries('Line', {
        color: '#f59e0b', // warning-500
        lineWidth: 2,
        title: '%D',
      });

      // Add overbought/oversold lines
      const stochOverboughtSeries = stochasticChart.addSeries('Line', {
        color: '#ef4444', // danger-500
        lineWidth: 1,
        lineStyle: 2, // dashed
        title: 'Overbought (80)',
      });

      const stochOversoldSeries = stochasticChart.addSeries('Line', {
        color: '#22c55e', // success-500
        lineWidth: 1,
        lineStyle: 2, // dashed
        title: 'Oversold (20)',
      });

      const kData: LineData[] = timeData.map(time => ({
        time: Math.floor(time.getTime() / 1000) as any,
        value: indicators.stochastic.k,
      }));

      const dData: LineData[] = timeData.map(time => ({
        time: Math.floor(time.getTime() / 1000) as any,
        value: indicators.stochastic.d,
      }));

      const stochOverboughtData: LineData[] = timeData.map(time => ({
        time: Math.floor(time.getTime() / 1000) as any,
        value: 80,
      }));

      const stochOversoldData: LineData[] = timeData.map(time => ({
        time: Math.floor(time.getTime() / 1000) as any,
        value: 20,
      }));

      kSeries.setData(kData);
      dSeries.setData(dData);
      stochOverboughtSeries.setData(stochOverboughtData);
      stochOversoldSeries.setData(stochOversoldData);

      // Set Stochastic scale
      stochasticChart.priceScale('right').applyOptions({
        scaleMargins: {
          top: 0.1,
          bottom: 0.1,
        },
      });

      stochasticChartInstanceRef.current = stochasticChart;
    }

    // Handle resize
    const handleResize = () => {
      if (rsiChartRef.current) {
        // Handle RSI chart resize
      }
      if (macdChartRef.current) {
        // Handle MACD chart resize
      }
      if (stochasticChartRef.current) {
        // Handle Stochastic chart resize
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      // Cleanup charts on unmount
      if (rsiChartInstanceRef.current) {
        rsiChartInstanceRef.current.remove();
        rsiChartInstanceRef.current = null;
      }
      if (macdChartInstanceRef.current) {
        macdChartInstanceRef.current.remove();
        macdChartInstanceRef.current = null;
      }
      if (stochasticChartInstanceRef.current) {
        stochasticChartInstanceRef.current.remove();
        stochasticChartInstanceRef.current = null;
      }
    };
  }, [indicators, timeData, height]);

  return (
    <div className="space-y-4">
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-semibold text-gray-900">RSI (14)</h4>
          <div className="flex items-center space-x-4">
            <span className={`text-sm font-medium ${
              indicators.rsi > 70 ? 'text-danger-600' : 
              indicators.rsi < 30 ? 'text-success-600' : 'text-gray-900'
            }`}>
              {indicators.rsi.toFixed(1)}
            </span>
            <span className={`px-2 py-1 text-xs rounded-full ${
              indicators.rsi > 70 ? 'bg-danger-100 text-danger-800' : 
              indicators.rsi < 30 ? 'bg-success-100 text-success-800' : 'bg-gray-100 text-gray-800'
            }`}>
              {indicators.rsi > 70 ? 'Overbought' : indicators.rsi < 30 ? 'Oversold' : 'Neutral'}
            </span>
          </div>
        </div>
        <div ref={rsiChartRef} className="w-full" />
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-semibold text-gray-900">MACD (12,26,9)</h4>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              MACD: <span className="font-medium">{indicators.macd.macd.toFixed(3)}</span>
            </span>
            <span className="text-sm text-gray-600">
              Signal: <span className="font-medium">{indicators.macd.signal.toFixed(3)}</span>
            </span>
            <span className={`px-2 py-1 text-xs rounded-full ${
              indicators.macd.histogram > 0 ? 'bg-success-100 text-success-800' : 'bg-danger-100 text-danger-800'
            }`}>
              {indicators.macd.histogram > 0 ? 'Bullish' : 'Bearish'}
            </span>
          </div>
        </div>
        <div ref={macdChartRef} className="w-full" />
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-semibold text-gray-900">Stochastic (14,3,3)</h4>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              %K: <span className="font-medium">{indicators.stochastic.k.toFixed(1)}</span>
            </span>
            <span className="text-sm text-gray-600">
              %D: <span className="font-medium">{indicators.stochastic.d.toFixed(1)}</span>
            </span>
            <span className={`px-2 py-1 text-xs rounded-full ${
              indicators.stochastic.k > 80 ? 'bg-danger-100 text-danger-800' : 
              indicators.stochastic.k < 20 ? 'bg-success-100 text-success-800' : 'bg-gray-100 text-gray-800'
            }`}>
              {indicators.stochastic.k > 80 ? 'Overbought' : indicators.stochastic.k < 20 ? 'Oversold' : 'Neutral'}
            </span>
          </div>
        </div>
        <div ref={stochasticChartRef} className="w-full" />
      </div>

      {/* Additional Indicators Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h4 className="text-sm font-semibold text-gray-900 mb-3">Additional Indicators</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-xs text-gray-600">Williams %R</div>
            <div className={`text-sm font-medium ${
              indicators.williamsR.value < -80 ? 'text-success-600' : 
              indicators.williamsR.value > -20 ? 'text-danger-600' : 'text-gray-900'
            }`}>
              {indicators.williamsR.value.toFixed(1)}
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-gray-600">ATR</div>
            <div className="text-sm font-medium text-gray-900">
              {indicators.atr.toFixed(2)}
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-gray-600">OBV</div>
            <div className="text-sm font-medium text-gray-900">
              {indicators.volumeAnalysis.obv.toLocaleString()}
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-gray-600">Trend Strength</div>
            <div className="text-sm font-medium text-gray-900">
              {indicators.patternRecognition.trendStrength.toFixed(0)}%
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
