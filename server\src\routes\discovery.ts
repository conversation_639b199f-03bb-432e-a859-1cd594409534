import { Router } from 'express';
import { TickerDiscoveryService } from '../services/TickerDiscoveryService';
import { GlobalNewsService } from '../services/GlobalNewsService';
import { NewsImpactAnalysisService } from '../services/NewsImpactAnalysisService';
import { SETTINGS } from '../config/settings';
import { logger } from '../utils/logger';

const router = Router();

// Initialize services
const tickerDiscoveryService = new TickerDiscoveryService(
  process.env.NEWS_API_KEY || '',
  process.env.GOOGLE_AI_API_KEY || ''
);

const globalNewsService = new GlobalNewsService(process.env.NEWS_API_KEY || '');
const newsImpactService = new NewsImpactAnalysisService(process.env.GOOGLE_AI_API_KEY || '');

/**
 * GET /api/discovery/tickers
 * Discover new tickers from global news analysis
 */
router.get('/tickers', async (req, res) => {
  try {
    logger.info('Starting ticker discovery request');
    
    const discoveredTickers = await tickerDiscoveryService.discoverNewTickers();
    
    res.json({
      success: true,
      data: {
        tickers: discoveredTickers,
        count: discoveredTickers.length,
        discoveredAt: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Error in ticker discovery:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to discover new tickers',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/discovery/news
 * Get global news with market relevance filtering
 */
router.get('/news', async (req, res) => {
  try {
    const {
      categories = 'general,business,technology,health',
      hours = '24',
      maxArticles = '50',
      minRelevance = '0.6'
    } = req.query;

    const categoriesArray = (categories as string).split(',');
    const hoursBack = parseInt(hours as string);
    const maxArticlesNum = parseInt(maxArticles as string);
    const minRelevanceScore = parseFloat(minRelevance as string);

    logger.info('Fetching global news', {
      categories: categoriesArray,
      hoursBack,
      maxArticles: maxArticlesNum
    });

    const allNews = await globalNewsService.fetchGlobalNews(
      categoriesArray,
      hoursBack,
      maxArticlesNum
    );

    const relevantNews = globalNewsService.filterMarketRelevantNews(
      allNews,
      minRelevanceScore
    );

    res.json({
      success: true,
      data: {
        allNews: allNews.length,
        relevantNews: relevantNews.length,
        articles: relevantNews,
        filters: {
          categories: categoriesArray,
          hoursBack,
          minRelevanceScore
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching global news:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch global news',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/discovery/analyze
 * Analyze market impact of specific news articles
 */
router.post('/analyze', async (req, res) => {
  try {
    const { articles } = req.body;

    if (!articles || !Array.isArray(articles)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid request body. Expected array of articles.'
      });
    }

    logger.info('Analyzing market impact', { articleCount: articles.length });

    const analyzedArticles = await newsImpactService.batchAnalyzeArticles(articles, 3);

    // Extract insights
    const extractedTickers = newsImpactService.extractTickers(analyzedArticles);
    const sectorSummary = newsImpactService.getSectorImpactSummary(analyzedArticles);
    const highImpactOpportunities = newsImpactService.filterHighImpactOpportunities(
      analyzedArticles,
      60,
      70
    );

    return res.json({
      success: true,
      data: {
        analyzedArticles,
        insights: {
          extractedTickers,
          sectorSummary,
          highImpactOpportunities
        },
        summary: {
          totalArticles: articles.length,
          analyzedArticles: analyzedArticles.length,
          uniqueTickers: extractedTickers.length,
          affectedSectors: Object.keys(sectorSummary).length,
          tradingOpportunities: highImpactOpportunities.length
        }
      }
    });
  } catch (error) {
    logger.error('Error analyzing market impact:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to analyze market impact',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/discovery/stats
 * Get discovery statistics and performance metrics
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = await tickerDiscoveryService.getDiscoveryStats();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Error fetching discovery stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch discovery statistics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/discovery/news/search
 * Search news by keywords
 */
router.get('/news/search', async (req, res) => {
  try {
    const {
      keywords,
      hours = '24',
      maxArticles = '50'
    } = req.query;

    if (!keywords) {
      return res.status(400).json({
        success: false,
        error: 'Keywords parameter is required'
      });
    }

    const keywordsArray = (keywords as string).split(',').map(k => k.trim());
    const hoursBack = parseInt(hours as string);
    const maxArticlesNum = parseInt(maxArticles as string);

    logger.info('Searching news by keywords', {
      keywords: keywordsArray,
      hoursBack,
      maxArticles: maxArticlesNum
    });

    const searchResults = await globalNewsService.searchNews(
      keywordsArray,
      hoursBack,
      maxArticlesNum
    );

    return res.json({
      success: true,
      data: {
        articles: searchResults,
        count: searchResults.length,
        searchParams: {
          keywords: keywordsArray,
          hoursBack,
          maxArticles: maxArticlesNum
        }
      }
    });
  } catch (error) {
    logger.error('Error searching news:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to search news',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/discovery/news/category/:category
 * Get news by specific category
 */
router.get('/news/category/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const {
      hours = '24',
      maxArticles = '50'
    } = req.query;

    const hoursBack = parseInt(hours as string);
    const maxArticlesNum = parseInt(maxArticles as string);

    logger.info('Fetching news by category', {
      category,
      hoursBack,
      maxArticles: maxArticlesNum
    });

    const categoryNews = await globalNewsService.getNewsByCategory(
      category,
      hoursBack,
      maxArticlesNum
    );

    res.json({
      success: true,
      data: {
        articles: categoryNews,
        count: categoryNews.length,
        category,
        params: {
          hoursBack,
          maxArticles: maxArticlesNum
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching category news:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch category news',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/discovery/opportunities
 * Get trading opportunities from analyzed news
 */
router.post('/opportunities', async (req, res) => {
  try {
    const {
      analyzedArticles,
      minImpactScore = 60,
      minConfidence = 70
    } = req.body;

    if (!analyzedArticles || !Array.isArray(analyzedArticles)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid request body. Expected array of analyzed articles.'
      });
    }

    const opportunities = newsImpactService.filterHighImpactOpportunities(
      analyzedArticles,
      minImpactScore,
      minConfidence
    );

    const sectorSummary = newsImpactService.getSectorImpactSummary(analyzedArticles);
    const extractedTickers = newsImpactService.extractTickers(analyzedArticles);

    return res.json({
      success: true,
      data: {
        opportunities,
        sectorSummary,
        extractedTickers,
        filters: {
          minImpactScore,
          minConfidence
        },
        summary: {
          totalOpportunities: opportunities.length,
          affectedSectors: Object.keys(sectorSummary).length,
          uniqueTickers: extractedTickers.length
        }
      }
    });
  } catch (error) {
    logger.error('Error filtering opportunities:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to filter trading opportunities',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
