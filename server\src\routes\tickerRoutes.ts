import { Router, Request, Response } from 'express';
import { TickerDataModel } from '../models/TickerData';
import { LogEntryModel } from '../models/LogEntry';
import { ApiResponse, DashboardData, TickerData } from '../types';
import { logger } from '../utils/logger';
import yahooFinance from 'yahoo-finance2';

const router = Router();

/**
 * GET /api/tickers - Get all ticker data
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const tickers = await TickerDataModel.find().sort({ lastUpdated: -1 });
    
    const response: ApiResponse<TickerData[]> = {
      success: true,
      data: tickers,
      timestamp: new Date()
    };
    
    res.json(response);
  } catch (error) {
    logger.error('Error fetching tickers:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch ticker data',
      timestamp: new Date()
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/tickers/:ticker - Get specific ticker data
 */
router.get('/:ticker', async (req: Request, res: Response) => {
  try {
    const { ticker } = req.params;
    const tickerData = await TickerDataModel.findOne({ ticker: ticker.toUpperCase() });
    
    if (!tickerData) {
      const response: ApiResponse<null> = {
        success: false,
        error: `Ticker ${ticker} not found`,
        timestamp: new Date()
      };
      return res.status(404).json(response);
    }
    
    const response: ApiResponse<TickerData> = {
      success: true,
      data: tickerData,
      timestamp: new Date()
    };
    
    return res.json(response);
  } catch (error) {
    logger.error(`Error fetching ticker ${req.params.ticker}:`, error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch ticker data',
      timestamp: new Date()
    };
    return res.status(500).json(response);
  }
});

/**
 * GET /api/tickers/:ticker/history - Get historical data for a ticker
 */
router.get('/:ticker/history', async (req: Request, res: Response) => {
  try {
    const { ticker } = req.params;
    const today = new Date();
    const oneYearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());

    const history = await yahooFinance.historical(ticker.toUpperCase(), {
      period1: oneYearAgo.toISOString().split('T')[0],
      period2: today.toISOString().split('T')[0],
      interval: '1d',
    });

    const response: ApiResponse<any> = {
      success: true,
      data: history,
      timestamp: new Date(),
    };

    res.json(response);
  } catch (error) {
    logger.error(`Error fetching historical data for ${req.params.ticker}:`, error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch historical data',
      timestamp: new Date(),
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/tickers/dashboard/summary - Get dashboard summary
 */
router.get('/dashboard/summary', async (req: Request, res: Response) => {
  try {
    const tickers = await TickerDataModel.find().sort({ lastUpdated: -1 });
    
    const buySignals = tickers.filter(t => t.recommendation.action === 'Buy').length;
    const sellSignals = tickers.filter(t => t.recommendation.action === 'Sell').length;
    const holdSignals = tickers.filter(t => t.recommendation.action === 'Hold').length;
    
    // Calculate average sentiment
    const sentimentCounts = { Positive: 0, Neutral: 0, Negative: 0 };
    tickers.forEach(ticker => {
      sentimentCounts[ticker.recommendation.sentiment]++;
    });
    
    let averageSentiment: 'Positive' | 'Neutral' | 'Negative' = 'Neutral';
    if (sentimentCounts.Positive > sentimentCounts.Negative && sentimentCounts.Positive > sentimentCounts.Neutral) {
      averageSentiment = 'Positive';
    } else if (sentimentCounts.Negative > sentimentCounts.Positive && sentimentCounts.Negative > sentimentCounts.Neutral) {
      averageSentiment = 'Negative';
    }
    
    const dashboardData: DashboardData = {
      tickers,
      summary: {
        totalTickers: tickers.length,
        buySignals,
        sellSignals,
        holdSignals,
        averageSentiment
      },
      lastUpdate: new Date()
    };
    
    const response: ApiResponse<DashboardData> = {
      success: true,
      data: dashboardData,
      timestamp: new Date()
    };
    
    res.json(response);
  } catch (error) {
    logger.error('Error fetching dashboard summary:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch dashboard summary',
      timestamp: new Date()
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/tickers/logs - Get recent logs
 */
router.get('/logs', async (req: Request, res: Response) => {
  try {
    const { limit = 50, type, ticker } = req.query;
    
    const filter: any = {};
    if (type) filter.type = type;
    if (ticker) filter.ticker = ticker;
    
    const logs = await LogEntryModel.find(filter)
      .sort({ timestamp: -1 })
      .limit(parseInt(limit as string));
    
    const response: ApiResponse<any[]> = {
      success: true,
      data: logs,
      timestamp: new Date()
    };
    
    res.json(response);
  } catch (error) {
    logger.error('Error fetching logs:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch logs',
      timestamp: new Date()
    };
    res.status(500).json(response);
  }
});

export default router; 