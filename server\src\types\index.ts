export interface OHLCV {
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp: Date;
}

export interface TechnicalIndicators {
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  bollingerBands: {
    upper: number;
    middle: number;
    lower: number;
  };
  sma: {
    short: number;
    long: number;
    crossover: 'bullish' | 'bearish' | 'neutral';
  };
  atr: number;
  adx: number;
}

export interface AdvancedTechnicalIndicators extends TechnicalIndicators {
  fibonacci: {
    retracements: { level: number; price: number }[];
    extensions: { level: number; price: number }[];
  };
  supportResistance: {
    support: number[];
    resistance: number[];
    pivotPoint: number;
  };
  volumeAnalysis: {
    obv: number;
    volumeProfile: { price: number; volume: number }[];
    volumeTrend: 'increasing' | 'decreasing' | 'neutral';
  };
  patternRecognition: {
    patterns: string[];
    trendDirection: 'uptrend' | 'downtrend' | 'sideways';
    trendStrength: number;
  };
  multiTimeframe: {
    '1h': Partial<TechnicalIndicators>;
    '4h': Partial<TechnicalIndicators>;
    '1d': Partial<TechnicalIndicators>;
  };
  ichimoku: {
    tenkanSen: number;
    kijunSen: number;
    senkouSpanA: number;
    senkouSpanB: number;
    chikouSpan: number;
  };
  stochastic: {
    k: number;
    d: number;
    signal: SignalType;
  };
  williamsR: {
    value: number;
    signal: SignalType;
  };
}

export interface SignalStrength {
  overall: number;
  bullish: number;
  bearish: number;
  signals: {
    indicator: string;
    signal: SignalType;
    strength: number;
    weight: number;
  }[];
}

export interface SentimentAnalysis {
  sentiment: 'Positive' | 'Neutral' | 'Negative';
  confidence: 'High' | 'Medium' | 'Low';
  summary: string;
  shortTermImpact: boolean;
  reasoning: string;
}

export interface EnhancedSentimentAnalysis extends SentimentAnalysis {
  mediumTermImpact: boolean;
  longTermImpact: boolean;
  fundamentalImpact: string;
  technicalImplications: string;
  riskFactors: string[];
  catalysts: string[];
  sectorImplications: string;
  institutionalView: string;
  priceTargetImpact: string;
  volumeExpectation: string;
  optionsActivity: string;
  marketCapImpact: string;
  sourceCredibility: number;
  sentimentScore: number;
}

export interface MarketContext {
  marketCap: string;
  sector: string;
  priceAction: string;
  currentPrice: number;
  volume: number;
  marketRegime: 'bull' | 'bear' | 'sideways';
  sectorPerformance: number;
  vixLevel: number;
}

export interface NewsArticle {
  title: string;
  content: string;
  source: string;
  url: string;
  publishedAt: Date;
  sentiment?: SentimentAnalysis;
}

export interface TradeRecommendation {
  ticker: string;
  action: 'Buy' | 'Sell' | 'Hold';
  confidence: 'High' | 'Medium' | 'Low';
  technicalSignals: {
    rsi: 'bullish' | 'bearish' | 'neutral';
    macd: 'bullish' | 'bearish' | 'neutral';
    bollingerBands: 'bullish' | 'bearish' | 'neutral';
    sma: 'bullish' | 'bearish' | 'neutral';
  };
  sentiment: 'Positive' | 'Neutral' | 'Negative';
  reasoning: string;
  riskManagement: {
    stopLoss: number;
    takeProfit: number;
    positionSize: number;
    maxRisk: number;
  };
  timestamp: Date;
}

export interface TickerData {
  ticker: string;
  currentPrice: number;
  technicalIndicators: TechnicalIndicators;
  news: NewsArticle[];
  recommendation: TradeRecommendation;
  lastUpdated: Date;
}

export interface LogEntry {
  id: string;
  type: 'technical_analysis' | 'sentiment_analysis' | 'recommendation' | 'error';
  ticker?: string;
  message: string;
  data?: any;
  timestamp: Date;
}

export interface DashboardData {
  tickers: TickerData[];
  summary: {
    totalTickers: number;
    buySignals: number;
    sellSignals: number;
    holdSignals: number;
    averageSentiment: 'Positive' | 'Neutral' | 'Negative';
  };
  lastUpdate: Date;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}

export type SentimentType = 'Positive' | 'Neutral' | 'Negative';
export type ConfidenceType = 'High' | 'Medium' | 'Low';
export type ActionType = 'Buy' | 'Sell' | 'Hold';
export type SignalType = 'bullish' | 'bearish' | 'neutral'; 