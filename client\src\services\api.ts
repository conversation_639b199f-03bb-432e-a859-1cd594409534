import axios from 'axios';
import { ApiResponse, TickerData, DashboardData, LogEntry } from '../types';

const API_BASE_URL = '/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

export const tickerApi = {
  // Get all tickers
  getAll: async (): Promise<TickerData[]> => {
    const response = await api.get<ApiResponse<TickerData[]>>('/tickers');
    return response.data.data || [];
  },

  // Get specific ticker
  getByTicker: async (ticker: string): Promise<TickerData> => {
    const response = await api.get<ApiResponse<TickerData>>(`/tickers/${ticker}`);
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to fetch ticker data');
    }
    return response.data.data;
  },

  // Get historical data for a ticker
  getHistory: async (ticker: string): Promise<any[]> => {
    const response = await api.get<ApiResponse<any[]>>(`/tickers/${ticker}/history`);
    return response.data.data || [];
  },

  // Get dashboard summary
  getDashboardSummary: async (): Promise<DashboardData> => {
    const response = await api.get<ApiResponse<DashboardData>>('/tickers/dashboard/summary');
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to fetch dashboard data');
    }
    return response.data.data;
  },

  // Get logs
  getLogs: async (params?: { limit?: number; type?: string; ticker?: string }): Promise<LogEntry[]> => {
    const response = await api.get<ApiResponse<LogEntry[]>>('/tickers/logs', { params });
    return response.data.data || [];
  },
};

export const healthApi = {
  // Health check
  check: async () => {
    const response = await api.get('/health');
    return response.data;
  },
}; 