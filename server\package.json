{"name": "trading-bot-server", "version": "1.0.0", "description": "Backend server for Trading Bot", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@google/generative-ai": "^0.11.3", "axios": "^1.6.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "mongoose": "^8.0.3", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "rss-parser": "^3.13.0", "technicalindicators": "^3.1.0", "winston": "^3.11.0", "yahoo-finance2": "^2.8.1", "zod": "^3.25.67"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.4", "@types/node-cron": "^3.0.11", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.3"}}